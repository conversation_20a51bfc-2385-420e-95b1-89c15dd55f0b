import { createRouter , createWebHistory } from 'vue-router'

const router = createRouter({
	history: createWebHistory(),
	routes: [
		{
			path: '/',
			redirect: '/home',
		},
		{
			path: '/login',
			component: () => import('@/views/login/login.vue')
		},

		{
			path: '/index',
			name:'index',
			redirect: '/home',
			children:[
				{
					path: '/home',
					component: () => import('@/layout/home.vue')
				},
				{
					path: '/workOrder/workOrderList',
					component: () => import('@/views/workOrder/workOrderList.vue')
				},
				{
					path: '/job/list',
					component: () => import('@/views/job/list.vue')
				}
			],
			component: () => import('@/layout/index.vue')
		}
	]
})

export default router
