<template>
  <div class="order-list-container">
    <div class="order-list-content">
      <!-- 搜索区域 -->
      <div class="card card--search search-flex">
        <el-input
          v-model="searchModel.goodsTitle"
          placeholder="商品标题"
          clearable
          style="width: 200px; margin-right: 16px;"
        />
        <el-input
          v-model="searchModel.buyerName"
          placeholder="购买者姓名"
          clearable
          style="width: 150px; margin-right: 16px;"
        />
        <el-select
          v-model="searchModel.status"
          placeholder="订单状态"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option label="待处理" value="0" />
          <el-option label="已完成" value="1" />
          <el-option label="已取消" value="2" />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="orderList"
            row-key="id"
            style="width: 100%; height: 100%;"
            class="data-table"
            v-loading="loading"
          >
            <el-table-column prop="id" label="订单ID" width="100" align="center"/>
            <el-table-column prop="goodsTitle" label="商品标题" align="center" min-width="150" show-overflow-tooltip/>
            <el-table-column prop="goodsPrice" label="商品价格" width="100" align="center">
              <template #default="scope">
                <span class="price-text">¥{{ scope.row.goodsPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="buyerName" label="购买者" width="120" align="center"/>
            <el-table-column prop="buyerPhone" label="联系电话" width="130" align="center"/>
            <el-table-column prop="sellerName" label="卖家" width="120" align="center"/>
            <el-table-column prop="sellerPhone" label="卖家电话" width="130" align="center"/>
            <el-table-column prop="status" label="订单状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" width="160" align="center"/>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button type="text" size="small" @click="viewDetail(scope.row)">查看</el-button>
                <el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="currentChange"
            :current-page="searchModel.pageNum"
            :page-size="searchModel.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="商品标题">{{ detailDialog.data.goodsTitle }}</el-descriptions-item>
          <el-descriptions-item label="商品价格">
            <span class="price-text">¥{{ detailDialog.data.goodsPrice }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(detailDialog.data.status)" size="small">
              {{ getStatusText(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="购买者">{{ detailDialog.data.buyerName }}</el-descriptions-item>
          <el-descriptions-item label="购买者电话">{{ detailDialog.data.buyerPhone }}</el-descriptions-item>
          <el-descriptions-item label="卖家">{{ detailDialog.data.sellerName }}</el-descriptions-item>
          <el-descriptions-item label="卖家电话">{{ detailDialog.data.sellerPhone }}</el-descriptions-item>
          <el-descriptions-item label="下单时间" :span="2">{{ detailDialog.data.createTime }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            <div class="description-text">{{ detailDialog.data.note || '无' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listGoodStuffOrder, getGoodStuffOrder, deleteGoodStuffOrder } from '@/api/goods/manageGoodStuff'

export default {
  name: 'GoodsOrderList',

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        goodsTitle: '',
        buyerName: '',
        status: ''
      },
      // 订单列表
      orderList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '订单详情',
        data: null
      }
    }
  },

  methods: {
    /**
     * 搜索订单列表
     */
    search() {
      this.loading = true

      // 构建查询参数，过滤空值
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listGoodStuffOrder(params).then(res => {
        this.orderList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取订单列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取订单列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        goodsTitle: '',
        buyerName: '',
        status: ''
      }
      this.search()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getGoodStuffOrder(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取订单详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取订单详情失败')
      })
    },

    /**
     * 删除订单
     */
    deleted(id) {
      this.$confirm('确定要删除这个订单吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGoodStuffOrder(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除订单失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 待处理
        1: 'success', // 已完成
        2: 'danger'   // 已取消
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        0: '待处理',
        1: '已完成',
        2: '已取消'
      }
      return statusMap[status] || '未知'
    }
  },

  created() {
    this.search()
  }
}
</script>

<style scoped>
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.order-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.card--search {
  flex-shrink: 0;
}

.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.price-text {
  color: #e74c3c;
  font-weight: bold;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.description-text {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
  word-break: break-all;
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

.dark-theme .price-text {
  color: #ff6b6b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
