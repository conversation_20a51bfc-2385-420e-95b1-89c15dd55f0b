<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="600px" class="payment-items-edit-dialog">
    <el-form :model="paymentItemsModel" :rules="rules" ref="formRef" label-width="120px" class="payment-items-edit-form">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="缴费项目名称" prop="paymentItemName">
            <el-input v-model="paymentItemsModel.paymentItemName" maxlength="100" placeholder="请输入缴费项目名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目描述" prop="paymentItemDescribe">
            <el-input v-model="paymentItemsModel.paymentItemDescribe" type="textarea" :rows="3" 
              maxlength="300" placeholder="请输入缴费项目描述" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input-number v-model="paymentItemsModel.amount" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区" prop="communityId">
            <el-select v-model="paymentItemsModel.communityId" placeholder="请选择小区" 
              style="width: 100%;" filterable>
              <el-option 
                v-for="item in communityList" 
                :key="item.id" 
                :label="item.communityName" 
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentItems, editPropertyPaymentItems } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'

export default {
  name: 'paymentItemsEdit',
  data() {
    return {
      paymentItemsModel: {},
      communityList: [],
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        paymentItemName: [
          { required: true, message: '请输入缴费项目名称', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' }
        ],
        communityId: [
          { required: true, message: '请选择小区', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 1000 }).then(res => {
        this.communityList = res.data.data.list || []
      })
    },
    
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.paymentItemsModel.id ? editPropertyPaymentItems : addPropertyPaymentItems
        api(this.paymentItemsModel).then(() => {
          this.$emit('search')
          this.dialog.show = false
          this.$message.success('操作成功')
        })
      })
    }
  },
  mounted() {
    this.loadCommunityList()
    
    mitt.on('openPaymentItemsEdit', (data) => {
      this.paymentItemsModel = { ...data }
      this.dialog.show = true
      this.dialog.title = '编辑缴费项目'
    })

    mitt.on('openPaymentItemsAdd', () => {
      this.paymentItemsModel = {}
      this.dialog.show = true
      this.dialog.title = '新增缴费项目'
    })
  },
  beforeDestroy() {
    mitt.off('openPaymentItemsEdit')
    mitt.off('openPaymentItemsAdd')
  }
}
</script>

<style scoped>
.payment-items-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.payment-items-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}
</style> 