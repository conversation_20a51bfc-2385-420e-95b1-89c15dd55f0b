<template>
  <div class="community-list-container">
    <community-edit @search="search" ref="editDialog" />
    <div class="card card--search search-flex">
      <el-input v-model="searchModel.communityName" placeholder="小区名称" clearable style="width: 200px; margin-right: 16px;" />
      <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
      <el-button type="primary" @click="add">添加</el-button>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table :data="communityList" row-key="id" align="center" style="width: 100%; height: 100%;" class="data-table">
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <el-table-column prop="communityName" label="小区名称" align="center"/>
          <el-table-column prop="address" label="地址" align="center"/>
          <el-table-column prop="lng" label="经度" width="100" align="center"/>
          <el-table-column prop="lat" label="纬度" width="100" align="center"/>
          <el-table-column prop="sort" label="序号" width="80" align="center"/>
          <el-table-column prop="note" label="备注" align="center"/>
          <el-table-column prop="createTime" label="创建时间" align="center"/>
          <el-table-column prop="updateTime" label="修改时间" align="center"/>
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
      </div>
    </div>
  </div>
</template>

<script>
import { listCommunity, deleteCommunity, getCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'
import communityEdit from '@/components/community/communityEdit.vue'
export default {
  components: { communityEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        communityName: ''
      },
      communityList: [],
      total: 0
    }
  },
  methods: {
    search() {
      listCommunity(this.searchModel).then(res => {
        this.communityList = res.data.data.list
        this.total = res.data.data.total
      })
    },
    add() {
      mitt.emit('openCommunityEdit')
    },
    edit(id) {
      getCommunity(id).then(res => {
        mitt.emit('openCommunityEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除小区, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCommunity(id).then(res => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  created() {
    this.search()
  }
}
</script>

<style scoped>
.community-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style> 