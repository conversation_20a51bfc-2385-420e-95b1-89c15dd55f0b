<template>
  <div class="job-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchModel" inline>
        <el-form-item label="任务名称">
          <el-input
            v-model="searchModel.jobName"
            placeholder="请输入任务名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="任务组">
          <el-select
            v-model="searchModel.jobGroup"
            placeholder="请选择任务组"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="group in jobGroupOptions"
              :key="group.value"
              :label="group.label"
              :value="group.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchModel.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search" icon="Search">搜索</el-button>
          <el-button @click="resetSearch" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="openJobEdit()" icon="Plus">
          新增任务
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button @click="search" icon="Refresh">刷新</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      :data="jobList"
      v-loading="loading"
      border
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="任务ID" width="80" />
      <el-table-column prop="jobName" label="任务名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="jobGroup" label="任务组" width="120">
        <template #default="scope">
          {{ getJobGroupLabel(scope.row.jobGroup) }}
        </template>
      </el-table-column>
      <el-table-column prop="invokeTarget" label="调用目标" min-width="200" show-overflow-tooltip />
      <el-table-column prop="cronExpression" label="Cron表达式" width="150" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" />
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="openJobDetail(scope.row)"
            icon="View"
          >
            详情
          </el-button>
          <el-button
            type="success"
            size="small"
            @click="openJobEdit(scope.row)"
            icon="Edit"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.status === 'stop'"
            type="warning"
            size="small"
            @click="handleStart(scope.row)"
            icon="VideoPlay"
          >
            启动
          </el-button>
          <el-button
            v-if="scope.row.status === 'run'"
            type="info"
            size="small"
            @click="handleStop(scope.row)"
            icon="VideoPause"
          >
            停止
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleRun(scope.row)"
            icon="CaretRight"
          >
            执行
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(scope.row)"
            icon="Delete"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="searchModel.pageNum"
        v-model:page-size="searchModel.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑对话框 -->
    <JobEdit ref="jobEditRef" @refresh="search" />

    <!-- 详情对话框 -->
    <JobDetail ref="jobDetailRef" />
  </div>
</template>

<script>
import {
  listJob,
  deleteJob,
  startJob,
  stopJob,
  runJob,
  JOB_STATUS,
  MISFIRE_POLICY,
  CONCURRENT
} from '@/api/job'
import { listDictByNameEn } from '@/api/system/dict'
import JobEdit from './components/JobEdit.vue'
import JobDetail from './components/JobDetail.vue'

export default {
  name: 'JobList',
  components: {
    JobEdit,
    JobDetail
  },
  data() {
    return {
      jobList: [],
      loading: false,
      total: 0,
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: ''
      },
      // 字典数据
      statusOptions: [],
      jobGroupOptions: []
    }
  },

  created() {
    this.search()
    this.initDictData()
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载任务状态字典
        const statusRes = await listDictByNameEn('job_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载任务组字典
        const groupRes = await listDictByNameEn('jobGroups')
        this.jobGroupOptions = (groupRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认数据
        this.statusOptions = JOB_STATUS
        this.jobGroupOptions = [
          { value: 'default', label: '默认组' },
          { value: 'system', label: '系统组' },
          { value: 'business', label: '业务组' }
        ]
      }
    },

    /**
     * 搜索任务列表
     */
    search() {
      this.loading = true
      const params = { ...this.searchModel }
      
      // 清理空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      listJob(params)
        .then(res => {
          this.jobList = res.data.data.list || []
          this.total = res.data.data.total || 0
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '查询失败')
          this.jobList = []
          this.total = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: ''
      }
      this.search()
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(size) {
      this.searchModel.pageSize = size
      this.searchModel.pageNum = 1
      this.search()
    },

    /**
     * 当前页变化
     */
    handleCurrentChange(page) {
      this.searchModel.pageNum = page
      this.search()
    },

    /**
     * 打开任务编辑对话框
     */
    openJobEdit(job = null) {
      this.$refs.jobEditRef.open(job)
    },

    /**
     * 打开任务详情对话框
     */
    openJobDetail(job) {
      this.$refs.jobDetailRef.open(job)
    },

    /**
     * 启动任务
     */
    handleStart(job) {
      this.$confirm(`确定要启动任务"${job.jobName}"吗？`, '启动确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        startJob(job.id)
          .then(() => {
            this.$message.success('启动成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '启动失败')
          })
      })
    },

    /**
     * 停止任务
     */
    handleStop(job) {
      this.$confirm(`确定要停止任务"${job.jobName}"吗？`, '停止确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        stopJob(job.id)
          .then(() => {
            this.$message.success('停止成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '停止失败')
          })
      })
    },

    /**
     * 立即执行任务
     */
    handleRun(job) {
      this.$confirm(`确定要立即执行任务"${job.jobName}"吗？`, '执行确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        runJob(job.id)
          .then(() => {
            this.$message.success('执行成功')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '执行失败')
          })
      })
    },

    /**
     * 删除任务
     */
    handleDelete(job) {
      this.$confirm(`确定要删除任务"${job.jobName}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteJob(job.id)
          .then(() => {
            this.$message.success('删除成功')
            this.search()
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '删除失败')
          })
      })
    },

    /**
     * 获取任务组标签文本
     */
    getJobGroupLabel(group) {
      const item = this.jobGroupOptions.find(item => item.value === group)
      return item ? item.label : group
    },

    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const tagMap = {
        'run': 'success',
        'stop': 'danger'
      }
      return tagMap[status] || ''
    },

    /**
     * 获取状态标签文本
     */
    getStatusLabel(status) {
      const item = this.statusOptions.find(item => item.value === status)
      return item ? item.label : status
    }
  }
}
</script>

<style scoped>
.job-list {
  padding: 20px;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
