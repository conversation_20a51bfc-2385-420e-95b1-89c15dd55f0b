<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="700px" class="vehicle-edit-dialog">
    <el-form :model="vehicleModel" :rules="rules" ref="formRef" label-width="120px" class="vehicle-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="车牌号" prop="plateNumber">
            <el-input v-model="vehicleModel.plateNumber" placeholder="请输入车牌号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车辆颜色" prop="vehicleColor">
            <el-select v-model="vehicleModel.vehicleColor" placeholder="请选择车辆颜色" style="width: 100%;">
              <el-option label="蓝色" value="蓝色" />
              <el-option label="黄色" value="黄色" />
              <el-option label="白色" value="白色" />
              <el-option label="黑色" value="黑色" />
              <el-option label="绿色" value="绿色" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车位类型" prop="parkingType">
            <el-input v-model="vehicleModel.parkingType" placeholder="请输入车位类型" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车位编号" prop="parkingNumber">
            <el-input v-model="vehicleModel.parkingNumber" placeholder="请输入车位编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期开始" prop="validBeginTime">
            <el-date-picker
              v-model="vehicleModel.validBeginTime"
              type="datetime"
              placeholder="请选择有效期开始时间"
              style="width: 100%;"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期结束" prop="validEndTime">
            <el-date-picker
              v-model="vehicleModel.validEndTime"
              type="datetime"
              placeholder="请选择有效期结束时间"
              style="width: 100%;"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="vehicleModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option label="正常" value="normal" />
              <el-option label="禁用" value="disabled" />
              <el-option label="过期" value="expired" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主要使用" prop="mainUse">
            <el-switch v-model="vehicleModel.mainUse" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="住户ID" prop="residentId">
            <el-input-number v-model="vehicleModel.residentId" :min="0" style="width: 100%;" placeholder="住户ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="小区ID" prop="communityId">
            <el-input-number v-model="vehicleModel.communityId" :min="0" style="width: 100%;" placeholder="小区ID" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="vehicleModel.note" type="textarea" :rows="3" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
   
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialog.show = false">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addCommunityVehicle, editCommunityVehicle } from '@/api/community/communityVehicle'
import mitt from '@/utils/mitt'

export default {
  name: 'communityVehicleEdit',
  data() {
    return {
      vehicleModel: {
        id: undefined,
        plateNumber: '',
        vehicleColor: '',
        parkingType: '',
        parkingNumber: '',
        validBeginTime: null,
        validEndTime: null,
        note: '',
        status: 'normal',
        mainUse: false,
        residentId: null,
        communityId: null
      },
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        plateNumber: [
          { required: true, message: '请输入车牌号', trigger: 'blur' }
        ],
        plateColor: [
          { required: true, message: '请选择车牌颜色', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.vehicleModel = {
        id: undefined,
        plateNumber: '',
        plateColor: '',
        parkingType: '',
        parkingNumber: '',
        validBeginTime: null,
        validEndTime: null,
        note: '',
        status: 'normal',
        mainUse: false,
        residentId: null,
        communityId: null
      }
    },
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.vehicleModel.id ? editCommunityVehicle : addCommunityVehicle
        api(this.vehicleModel).then(() => {
          this.$message.success('保存成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '保存失败')
        })
      })
    }
  },
  mounted() {
    mitt.on('openCommunityVehicleEdit', (data) => {
      this.resetForm()
      if (data && data.id) {
        this.vehicleModel = { ...data }
        this.dialog.title = '编辑车辆信息'
      } else {
        this.dialog.title = '新增车辆信息'
      }
      this.dialog.show = true
    })
  },
  beforeDestroy() {
    mitt.off('openCommunityVehicleEdit')
  }
}
</script>

<style scoped>
.vehicle-edit-dialog >>> .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}
.vehicle-edit-form {
  padding: 0 10px;
}
.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}
</style> 