<template>
  <div class="user-list-container">
    <user-edit
      :sexList="sexList"
      :statusList="statusList"
      :roleList="roleList"
      @search="search"
    ></user-edit>
    <div class="card card--search search-flex">
      <el-input
        v-model="searchModel.keyword"
        placeholder="用户名|昵称"
        clearable
        style="width: 200px; margin-right: 16px"
      />
      <el-button type="primary" @click="search" style="margin-right: 8px"
        >搜索</el-button
      >
      <el-button type="primary" @click="add">添加</el-button>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table
          stripe
          :data="userList"
          style="width: 100%; height: 100%"
          class="data-table"
        >
          <el-table-column prop="userName" align="center" label="用户名" />
          <el-table-column prop="nickName" align="center" label="昵称" />
		  <el-table-column
            prop="role"
            align="center"
            label="角色"
            :formatter="formatRole"
          />
          <el-table-column
            prop="gender"
            align="center"
            label="性别"
            :formatter="formatSex"
          />
          <el-table-column prop="birthday" align="center" label="生日" />
          <el-table-column prop="createTime" align="center" label="创建时间" />
          <el-table-column prop="updateTime" align="center" label="更新时间" />
          <el-table-column align="center" width="160" label="操作">
            <template #default="scope">
              <el-button type="text" size="mini" @click="edit(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" size="mini" @click="deleted(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination
          background
          layout="prev, pager, next"
          @current-change="currentChange"
          @prev-click="prevClick"
          @next-click="nextClick"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>

  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="600px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      :model="userModel"
      :rules="rules"
      ref="formRef"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="userModel.userName"
              placeholder="请输入用户名"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昵称" prop="nickName">
            <el-input
              v-model="userModel.nickName"
              placeholder="请输入昵称"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
		<el-col :span="12">
          <el-form-item label="角色" prop="role">
            <el-input
              v-model="userModel.role"
              placeholder="请输入角色"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="userModel.phone"
              placeholder="请输入手机号"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
		<el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-input
              v-model="userModel.gender"
              placeholder="请输入性别"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
		<el-col :span="12">
          <el-form-item label="生日" prop="birthday">
            <el-input
              v-model="userModel.birthday"
              placeholder="请输入生日"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>



      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { listUser, deleteUser, getUser } from "@/api/system/miniUser";
import { listDictByNameEn } from "@/api/system/dict";
import { listRole } from "@/api/system/role";
import mitt from "@/utils/mitt";
import userEdit from "@/components/system/userEdit.vue";
export default {
  components: { userEdit },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
      },
      userList: [],
      roleList: [],
      statusList: [],
      sexList: [],
      total: 0,
      dialog: {
        show: false,
        title: "编辑用户",
      },
      userModel: {
        id: '',
        birthday: "",
        userName: "",
        nickName: "",
        avatarUrl: "",
        gender: "",
        phone: "",
        residentId: '',
      },
      rules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        roleId: [{ required: true, message: "请选择角色", trigger: "change" }],
      },
    };
  },
  methods: {
    handleClose() {
      this.dialog.show = false;
    },
    search() {
      listUser(this.searchModel)
        .then((res) => {
          this.userList = res.data.data.list;
          this.total = res.data.data.total;
        })
        .catch((err) => {
          this.$message.error(err.data.errorMessage);
        });
    },
    add() {
      this.dialog.show = true;
    },
    edit(row) {
		this.userModel=row
      this.dialog.show = true;
    },
    deleted(id) {
      this.$confirm("删除用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteUser(id)
            .then((res) => {
              this.search();
              this.$message.success("操作成功");
            })
            .catch((err) => {
              this.$message.error(err.data.errorMessage);
            });
        })
        .catch(() => {});
    },
    currentChange(num) {
      this.searchModel.pageNum = num;
      this.search();
    },
    prevClick(newPage) {
      this.searchModel.pageNum = newPage;
      this.search();
    },
    nextClick(newPage) {
      this.searchModel.pageNum = newPage;
      this.search();
    },
    formatStatus(row, column, cellValue, index) {
      let result = "";
      for (let item of this.statusList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
          break;
        }
      }
      return result;
    },

	formatRole()
	{

	},

    formatSex(row, column, cellValue, index) {
      let result = "";
      for (let item of this.sexList) {
        if (item.nameEn == cellValue) {
          result = item.nameCn;
          break;
        }
      }
      return result;
    },
    async init() {
      try {
        const [sex_res, status_res, user_res, role_res] = await Promise.all([
          listDictByNameEn("sex"),
          listDictByNameEn("user_status"),
          listUser(this.searchModel),
          listRole(),
        ]);

        this.sexList = sex_res.data.data;
        this.statusList = status_res.data.data;
        this.userList = user_res.data.data.list;
        this.roleList = role_res.data.data.list;
        this.total = user_res.data.data.total;
      } catch (err) {
        this.$message.error(err.data.errorMessage);
      }
    },
  },
  created() {
    this.init();
  },
  unmounted() {
    mitt.off("openUserAdd");
    mitt.off("openUserEdit");
  },
};
</script>

<style scoped>
.user-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.card--table {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-top: 0;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.search-flex {
  display: flex;
  align-items: center;
}

.card--search {
  margin-bottom: 20px;
  flex: none;
  height: auto;
  padding: 20px 20px;
  display: flex;
  align-items: center;
}
</style>