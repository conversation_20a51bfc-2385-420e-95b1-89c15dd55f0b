import request from '@/utils/request'

// 分页查询物业缴费项目
export function listPropertyPaymentItems(params) {
  return request({
    url: '/manage-api/v1/property/payment-items/page',
    method: 'get',
    params
  })
}

// 通过ID查询物业缴费项目
export function getPropertyPaymentItems(id) {
  return request({
    url: '/manage-api/v1/property/payment-items',
    method: 'get',
    params: { id }
  })
}

// 新增物业缴费项目
export function addPropertyPaymentItems(data) {
  return request({
    url: '/manage-api/v1/property/payment-items',
    method: 'post',
    params: data
  })
}

// 编辑物业缴费项目
export function editPropertyPaymentItems(data) {
  return request({
    url: '/manage-api/v1/property/payment-items',
    method: 'put',
    params: data
  })
}

// 删除物业缴费项目
export function deletePropertyPaymentItems(id) {
  return request({
    url: '/manage-api/v1/property/payment-items',
    method: 'delete',
    params: { id }
  })
} 