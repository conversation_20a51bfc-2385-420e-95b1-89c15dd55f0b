<template>
	<el-dialog
		:title="dialog.title"
		v-model="dialog.show"
		width="600px"
		destroy-on-close
		@close="handleClose">
		<el-form :model="userModel" :rules="rules" ref="formRef" label-width="100px">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="用户名" prop="userName">
						<el-input v-model="userModel.userName" placeholder="请输入用户名" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="昵称" prop="nickName">
						<el-input v-model="userModel.nickName" placeholder="请输入昵称" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="手机号" prop="phone">
						<el-input v-model="userModel.phone" placeholder="请输入手机号" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="邮箱" prop="email">
						<el-input v-model="userModel.email" placeholder="请输入邮箱" clearable></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="性别" prop="sex">
						<el-select v-model="userModel.sex" placeholder="请选择性别" style="width: 100%;">
							<el-option v-for="item in sexList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="状态" prop="status">
						<el-select v-model="userModel.status" placeholder="请选择状态" style="width: 100%;">
							<el-option v-for="item in statusList" :key="item.nameEn" :label="item.nameCn" :value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="角色" prop="roleId">
						<el-select v-model="userModel.roleId" placeholder="请选择角色" style="width: 100%;">
							<el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col v-if="!userModel.id" :span="12">
					<el-form-item label="密码" prop="password">
						<el-input type="password" v-model="userModel.password" placeholder="请输入密码" clearable show-password></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialog.show = false">取消</el-button>
				<el-button type="primary" @click="submit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>
<script>
import { addUser, editUser } from '@/api/system/user'
import mitt from '@/utils/mitt'
export default {
	props: ['sexList', 'statusList', 'roleList'],
	data() {
		return {
			userModel: {
				id: null,
				userName: '',
				nickName: '',
				phone: '',
				email: '',
				sex: '',
				status: '',
				roleId: null,
				orgId: null,
				password: ''
			},
			dialog: {
				show: false,
				title: ''
			},
			rules: {
				userName: [
					{ required: true, message: '请输入用户名', trigger: 'blur' }
				],
				roleId: [
					{ required: true, message: '请选择角色', trigger: 'change' }
				]
			}
		}
	},
	methods: {
		submit() {
			this.$refs.formRef.validate(valid => {
				if (!valid) return
				if (this.userModel.id) {
					editUser(this.userModel)
						.then(res => {
							this.$message.success('修改成功')
							this.dialog.show = false
							this.$emit('search')
						})
						.catch(err => {
							this.$message.error(err.data.errorMessage)
						})
				} else {
					addUser(this.userModel)
						.then(res => {
							this.$message.success('添加成功')
							this.dialog.show = false
							this.$emit('search')
						})
						.catch(err => {
							this.$message.error(err.data.errorMessage)
						})
				}
			})
		},
		handleClose() {
			this.resetForm()
		},
		resetForm() {
			this.$refs.formRef && this.$refs.formRef.resetFields()
			this.userModel = {
				id: null,
				userName: '',
				nickName: '',
				phone: '',
				email: '',
				sex: '',
				status: '',
				roleId: null,
				orgId: null,
				password: ''
			}
		}
	},
	mounted() {
		mitt.on('openUserAdd', () => {
			this.resetForm()
			this.dialog.title = '新增用户'
			this.dialog.show = true
		})
		mitt.on('openUserEdit', (data) => {
			this.resetForm()
			this.userModel = { ...data }
			this.dialog.title = '编辑用户'
			this.dialog.show = true
		})
	}
}
</script>
<style scoped>
</style> 