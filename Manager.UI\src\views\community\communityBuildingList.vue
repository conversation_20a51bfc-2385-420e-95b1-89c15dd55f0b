<template>
  <div class="building-list-container">
    <community-building-edit @search="search" ref="editDialog" />
    <community-building-detail ref="detailDialog" />
    <div class="card card--search search-flex">
      <el-input v-model="searchModel.buildingNumber" placeholder="楼栋名称" clearable style="width: 200px; margin-right: 16px;" />
      <!-- <el-select v-model="searchModel.type" placeholder="类型" clearable style="width: 150px; margin-right: 16px;">
        <el-option label="楼栋" value="building" />
        <el-option label="单元" value="unit" />
        <el-option label="房间" value="room" />
      </el-select> -->
      <el-select v-model="searchModel.communityId" placeholder="选择小区" clearable style="width: 200px; margin-right: 16px;" @change="onCommunityChange">
        <el-option v-for="community in communityList" :key="community.id" :label="community.communityName" :value="community.id" />
      </el-select>
      <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
      <el-button type="primary" @click="add">添加</el-button>
    </div>
    <div class="card card--table">
      <div class="table-col">
        <el-table :data="buildingList" row-key="id" align="center" style="width: 100%; height: 100%;" class="data-table">
          <el-table-column prop="buildingNumber" label="楼栋名称" align="center"/>
          <el-table-column prop="id" label="ID" width="80" align="center"/>
          <!-- <el-table-column prop="type" label="类型" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.type === 'building'" type="primary">楼栋</el-tag>
              <el-tag v-else-if="scope.row.type === 'unit'" type="success">单元</el-tag>
              <el-tag v-else-if="scope.row.type === 'room'" type="warning">房间</el-tag>
              <el-tag v-else type="info">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column prop="sort" label="序号" width="80" align="center"/>
          <el-table-column prop="createTime" label="创建时间" align="center"/>
          <el-table-column prop="updateTime" label="修改时间" align="center"/>
          <el-table-column prop="note" label="备注" align="center"/>
          <el-table-column label="操作" width="250">
            <template #default="scope">
              <el-button  type="text" size="mini" @click="showDetail(scope.row)">详情</el-button>
              <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-col">
        <el-pagination background layout="prev, pager, next" @current-change="currentChange" :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
      </div>
    </div>
  </div>
</template>

<script>
import { listCommunityBuilding, deleteCommunityBuilding, getCommunityBuilding } from '@/api/community/communityBuilding'
import { listCommunity } from '@/api/community/community'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'
import communityBuildingEdit from '@/components/community/communityBuildingEdit.vue'
import communityBuildingDetail from '@/components/community/communityBuildingDetail.vue'
export default {
  components: { communityBuildingEdit, communityBuildingDetail },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        buildingNumber: '',
        type: '',
        communityId: null
      },
      buildingList: [],
      communityList: [],
      total: 0
    }
  },
  methods: {
    search() {
      // 自动使用全局选中的小区ID
      const selectedCommunityId = getSelectedCommunityId()
      const searchParams = {
        ...this.searchModel,
        communityId: selectedCommunityId
      }

      listCommunityBuilding(searchParams).then(res => {
        this.buildingList = res.data.data.list
        this.total = res.data.data.total
      }).catch(err => {
        console.error('查询楼栋列表失败:', err)
        this.$message.error(err.data?.errorMessage || '查询失败')
      })
    },
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {

        this.communityList = res.data.data.list
      })
    },
    onCommunityChange() {
      this.search()
    },
    showDetail(building) {
      mitt.emit('openCommunityBuildingDetail', building)
    },
    add() {
      mitt.emit('openCommunityBuildingEdit')
    },
    edit(id) {
      getCommunityBuilding(id).then(res => {
        mitt.emit('openCommunityBuildingEdit', res.data.data)
      })
    },
    deleted(id) {
      this.$confirm('删除楼房, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteCommunityBuilding(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        })
      }).catch(() => {})
    },
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },
    /**
     * 处理小区变化事件
     */
    handleCommunityChange() {
      // 小区变化时自动刷新楼栋列表
      this.search()
    }
  },
  created() {
    this.loadCommunityList()
    this.search()

    // 监听小区变化事件
    window.addEventListener('communityChanged', this.handleCommunityChange)
  },

  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('communityChanged', this.handleCommunityChange)
  }
}
</script>

<style scoped>
.building-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}
.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style>