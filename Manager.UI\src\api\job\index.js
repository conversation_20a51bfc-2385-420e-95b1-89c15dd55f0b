import request from '@/utils/request'

/**
 * 定时任务分页查询
 * @param {Object} data - 查询参数
 * @returns {Promise} 定时任务列表
 */
export const listJob = (data) =>
  request({
    url: '/manage-api/v1/job/page',
    method: 'get',
    params: data
  })

/**
 * 获取定时任务详情
 * @param {number} id - 任务ID
 * @returns {Promise} 任务详情
 */
export const getJob = (id) =>
  request({
    url: '/manage-api/v1/job',
    method: 'get',
    params: { id: id }
  })

/**
 * 新增定时任务
 * @param {Object} data - 任务数据
 * @returns {Promise} 新增结果
 */
export const addJob = (data) =>
  request({
    url: '/manage-api/v1/job',
    method: 'post',
    data: data
  })

/**
 * 编辑定时任务
 * @param {Object} data - 任务数据
 * @returns {Promise} 编辑结果
 */
export const editJob = (data) =>
  request({
    url: '/manage-api/v1/job',
    method: 'put',
    data: data
  })

/**
 * 删除定时任务
 * @param {number} id - 任务ID
 * @returns {Promise} 删除结果
 */
export const deleteJob = (id) =>
  request({
    url: '/manage-api/v1/job',
    method: 'delete',
    params: { id: id }
  })

/**
 * 启动定时任务
 * @param {number} id - 任务ID
 * @returns {Promise} 启动结果
 */
export const startJob = (id) =>
  request({
    url: `/manage-api/v1/job/start/${id}`,
    method: 'post'
  })

/**
 * 停止定时任务
 * @param {number} id - 任务ID
 * @returns {Promise} 停止结果
 */
export const stopJob = (id) =>
  request({
    url: `/manage-api/v1/job/stop/${id}`,
    method: 'post'
  })

/**
 * 立即执行定时任务
 * @param {number} id - 任务ID
 * @returns {Promise} 执行结果
 */
export const runJob = (id) =>
  request({
    url: `/manage-api/v1/job/run/${id}`,
    method: 'post'
  })

/**
 * 任务状态字典
 */
export const JOB_STATUS = [
  { value: 'run', label: '运行中' },
  { value: 'stop', label: '已停止' }
]

/**
 * 错失执行策略字典
 */
export const MISFIRE_POLICY = [
  { value: 'default', label: '默认策略' },
  { value: 'ignore', label: '忽略错失' },
  { value: 'fire_once', label: '立即执行一次' },
  { value: 'do_nothing', label: '不执行' }
]

/**
 * 并发执行字典
 */
export const CONCURRENT = [
  { value: true, label: '允许' },
  { value: false, label: '禁止' }
]
