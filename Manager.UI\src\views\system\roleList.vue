<template>
	<div class="role-list-container">
		<div class="role-list-content">
			<role-edit :menuList="menuList" @search="search"></role-edit>
			<div class="card card--search search-flex">
				<el-input v-model="searchModel.roleName" placeholder="角色名" clearable style="width: 200px; margin-right: 16px;" />
				<el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
				<el-button type="primary" @click="add">添加</el-button>
			</div>
			<div class="card card--table">
				<div class="table-col">
					<el-table stripe :data="roleList" style="width: 100%; height: 100%;" class="data-table">
						<el-table-column prop="roleName" align="center" label="角色名" width="180" />
						<el-table-column prop="roleCode" align="center" label="角色码" width="180" />
						<el-table-column prop="updateTime" align="center" label="更新时间" />
						<el-table-column align="center" width="200" label="操作">
							<template #default="scope">
								<el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
								<el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="pagination-col">
					<el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick"
						@next-click="nextClick" :total="total"></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { listRole, deleteRole, getRole } from "@/api/system/role"
import { listMenu } from "@/api/system/menu"
import mitt from "@/utils/mitt"
import roleEdit from "@/components/system/roleEdit.vue"
export default {
	components: { roleEdit },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10
			},
			roleList: [],
			menuList:[],
			total: 0
		}
	},
	methods: {
		search() {
			listRole(this.searchModel)
				.then(res => {
					this.roleList = res.data.data.list
					this.total = res.data.data.total
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		edit(id) {
			getRole(id)
				.then(res => {
					mitt.emit('openRoleEdit', res.data.data)
				}).catch((err) => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add() {
			mitt.emit('openRoleAdd')
		},
		deleted(id) {
			this.$confirm('删除角色, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteRole(id)
					.then(res => {
						this.search()
						this.$message.success("操作成功")
					}).catch((err) => {
						this.$message.error(err.data.errorMessage)

					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		nextClick(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		async init() {
			mitt.off('openRoleEdit')
			mitt.off('openRoleAdd')
			try {
				const [role_res, menu_res] = await Promise.all([
					listRole(this.searchModel),
					listMenu({ pageNum: 1, pageSize: 9999 })
				])
				this.roleList = role_res.data.data.list
				this.total = role_res.data.data.total
				this.menuList = menu_res.data.data.list
			} catch (err) {
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	}
}
</script>

<style scoped>
.role-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.role-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

/* 暗色主题样式 */
.dark-theme .card {
  background-color: var(--card-background);
  border: 1px solid #444;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}
</style> 