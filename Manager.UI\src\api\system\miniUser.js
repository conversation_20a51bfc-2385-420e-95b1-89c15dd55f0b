import request from '@/utils/request'

// 分页查询用户列表
export function listUser(data) {
    return request({
        url: '/manage-api/v1/member/page',
        method: 'get',
        params: data
    })
}


// 添加用户
export function addUser(data) {
    return request({
        url: '/manage-api/v1/member/add',
        method: 'post',
        data
    })
}

// 编辑用户
export function editUser(data) {
    return request({
        url: '/manage-api/v1/member/edit',
        method: 'post',
        data
    })
}

// 删除用户
export function deleteUser(id) {
    return request({
        url: '/manage-api/v1/member/delete/' + id,
        method: 'delete'
    })
}

// 获取用户详情
export function getUser(id) {
    return request({
        url: '/manage-api/v1/member?id=' + id,
        method: 'get'
    })
}
