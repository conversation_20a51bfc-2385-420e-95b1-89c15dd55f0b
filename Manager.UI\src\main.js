import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router/index'
import store from '@/store/index'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入全局样式
import '@/assets/styles/global.css'

// 导入小区变化监听全局混入
import communityMixin from '@/mixins/communityMixin.js'

const app = createApp(App)

// 全局注册小区变化监听混入
app.mixin(communityMixin)

// 设置全局配置，关闭警告信息
app.config.warnHandler = () => {};

app.use(router)
app.use(store)
app.use(ElementPlus)

app.mount('#app')
