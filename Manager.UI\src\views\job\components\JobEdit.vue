<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="form"
      :model="jobModel"
      :rules="rules"
      label-width="120px"
      class="job-edit-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="jobName">
            <el-input
              v-model="jobModel.jobName"
              placeholder="请输入任务名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="任务分组" prop="jobGroup">
            <el-select
              v-model="jobModel.jobGroup"
              placeholder="请选择任务分组"
              style="width: 100%"
            >
              <el-option
                v-for="group in jobGroupOptions"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="日志保留天数" prop="logSaveDay">
            <el-input-number
              v-model="jobModel.logSaveDay"
              :min="1"
              :max="365"
              placeholder="请输入日志保留天数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="调用方法" prop="invokeTarget">
            <el-input
              v-model="jobModel.invokeTarget"
              type="textarea"
              :rows="3"
              placeholder="请输入调用目标字符串"
              maxlength="500"
              show-word-limit
            />
            <div class="help-text">
              <strong>Bean用法示例：</strong> ryTask.ryParams('ry')<br>
              <strong>Class类调用示例：</strong> com.ruoyi.quartz.task.RyTask.ryParams('ry')<br>
              <strong>参数说明：</strong> 支持字符串，布尔类型，长整型，浮点型，整型
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="cron表达式" prop="cronExpression">
            <el-input
              v-model="jobModel.cronExpression"
              placeholder="请输入Cron执行表达式"
              maxlength="255"
            >
              <template #append>
                <el-button @click="showCronHelp">生成表达式</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="错误策略" prop="misfirePolicy">
            <el-select
              v-model="jobModel.misfirePolicy"
              placeholder="请选择错误策略"
              style="width: 100%"
            >
              <el-option
                v-for="policy in misfirePolicyOptions"
                :key="policy.value"
                :label="policy.label"
                :value="policy.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="是否并发" prop="concurrent">
            <el-radio-group v-model="jobModel.concurrent">
              <el-radio :value="true">允许</el-radio>
              <el-radio :value="false">禁止</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="jobModel.status">
              <el-radio value="run">运行</el-radio>
              <el-radio value="stop">停止</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="jobModel.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- Cron表达式帮助 -->
    <el-dialog
      title="Cron表达式生成器"
      v-model="cronHelpDialog"
      width="600px"
      append-to-body
    >
      <div class="cron-help">
        <h4>Cron表达式语法:</h4>
        <p><strong>[秒] [分] [小时] [日] [月] [周] [年]</strong></p>
        
        <h4>通配符说明:</h4>
        <ul>
          <li><strong>*</strong> 表示任何一个字段都匹配</li>
          <li><strong>?</strong> 表示不指定值，使用的场景为不需要关心当前设置这个字段的值</li>
          <li><strong>-</strong> 表示区间，例如在小时上设置 "10-12"，表示10,11,12点都会触发</li>
          <li><strong>,</strong> 表示指定多个值，例如在周字段上设置 "MON,WED,FRI" 表示周一，周三和周五触发</li>
          <li><strong>/</strong> 用于递增触发，如在秒上面设置"5/15" 表示从5秒开始，每隔15秒触发(5,20,35,50)</li>
          <li><strong>#</strong> 序号(表示每月的第几个周几)，例如在周字段上设置"6#3"表示在每月的第三个周六</li>
          <li><strong>L</strong> 表示最后的意思</li>
        </ul>

        <h4>常用示例:</h4>
        <ul>
          <li><strong>0 0 2 1 * ?</strong> 表示在每月的1日的凌晨2点调度任务</li>
          <li><strong>0 15 10 ? * MON-FRI</strong> 表示周一到周五每天上午10:15执行作业</li>
          <li><strong>0 15 10 15 * ?</strong> 表示每月15日上午10:15执行作业</li>
          <li><strong>0 0 12 * * ?</strong> 表示每天中午12点执行作业</li>
          <li><strong>0 0 10,14,16 * * ?</strong> 表示每天上午10点，下午2点，4点执行作业</li>
          <li><strong>0 0/30 9-17 * * ?</strong> 表示朝九晚五工作时间内每半小时执行一次作业</li>
          <li><strong>0 0 12 ? * WED</strong> 表示每个星期三中午12点执行作业</li>
        </ul>
      </div>
    </el-dialog>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  addJob,
  editJob,
  MISFIRE_POLICY,
  CONCURRENT
} from '@/api/job'
import { listDictByNameEn } from '@/api/system/dict'

export default {
  name: 'JobEdit',
  emits: ['refresh'],
  data() {
    return {
      dialog: {
        show: false,
        title: ''
      },
      cronHelpDialog: false,
      jobModel: {
        id: null,
        jobName: '',
        jobGroup: '',
        invokeTarget: '',
        cronExpression: '',
        misfirePolicy: 'default',
        concurrent: true,
        logSaveDay: 30,
        status: 'run',
        remark: ''
      },
      rules: {
        jobName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        jobGroup: [
          { required: true, message: '请选择任务分组', trigger: 'change' }
        ],
        invokeTarget: [
          { required: true, message: '请输入调用目标字符串', trigger: 'blur' }
        ],
        cronExpression: [
          { required: true, message: '请输入Cron执行表达式', trigger: 'blur' }
        ],
        misfirePolicy: [
          { required: true, message: '请选择错误策略', trigger: 'change' }
        ],
        concurrent: [
          { required: true, message: '请选择是否并发', trigger: 'change' }
        ],
        logSaveDay: [
          { required: true, message: '请输入日志保留天数', trigger: 'blur' }
        ]
      },
      submitting: false,
      // 字典数据
      jobGroupOptions: [],
      misfirePolicyOptions: MISFIRE_POLICY,
      concurrentOptions: CONCURRENT
    }
  },

  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载任务组字典
        const groupRes = await listDictByNameEn('jobGroups')
        this.jobGroupOptions = (groupRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载任务组字典失败:', err)
        // 使用默认数据
        this.jobGroupOptions = [
          { value: 'default', label: '默认组' },
          { value: 'system', label: '系统组' },
          { value: 'business', label: '业务组' }
        ]
      }
    },

    /**
     * 打开对话框
     */
    open(job = null) {
      // 初始化字典数据
      this.initDictData()

      if (job) {
        this.jobModel = { ...job }
        this.dialog.title = '编辑定时任务'
      } else {
        this.resetForm()
        this.dialog.title = '新增定时任务'
      }

      this.dialog.show = true
    },

    /**
     * 显示Cron表达式帮助
     */
    showCronHelp() {
      this.cronHelpDialog = true
    },

    /**
     * 提交表单
     */
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return

        this.submitting = true
        const api = this.jobModel.id ? editJob : addJob

        api(this.jobModel)
          .then(() => {
            this.$message.success('保存成功')
            this.dialog.show = false
            this.$emit('refresh')
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '保存失败')
          })
          .finally(() => {
            this.submitting = false
          })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.jobModel = {
        id: null,
        jobName: '',
        jobGroup: '',
        invokeTarget: '',
        cronExpression: '',
        misfirePolicy: 'default',
        concurrent: true,
        logSaveDay: 30,
        status: 'run',
        remark: ''
      }
    }
  }
}
</script>

<style scoped>
.job-edit-form {
  padding: 0 10px;
}

.help-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  line-height: 1.5;
}

.cron-help {
  font-size: 14px;
  line-height: 1.6;
}

.cron-help h4 {
  margin: 15px 0 10px 0;
  color: #333;
}

.cron-help ul {
  margin: 10px 0;
  padding-left: 20px;
}

.cron-help li {
  margin: 5px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
