/**
 * 全局小区存储 - 简化版本
 * 类似微信小程序的存储模式
 */

// 存储键名
const STORAGE_KEY = 'selectedCommunity'
const COMMUNITY_LIST_KEY = 'communityList'

// 全局状态
let selectedCommunity = null
let communityList = []
let changeListeners = []

// 初始化时从localStorage加载数据
try {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    selectedCommunity = JSON.parse(stored)
  }

  const storedList = localStorage.getItem(COMMUNITY_LIST_KEY)
  if (storedList) {
    communityList = JSON.parse(storedList)
  }
} catch (error) {
  console.warn('加载存储数据失败:', error)
}

// ==================== 核心API方法 ====================

/**
 * 获取当前选中的小区ID - 主要API
 * @returns {number|null} 小区ID
 */
export const getSelectedCommunityId = () => {
  return selectedCommunity?.id || null
}

/**
 * 获取当前选中的小区对象
 * @returns {Object|null} 小区对象
 */
export const getSelectedCommunity = () => {
  return selectedCommunity
}

/**
 * 设置选中的小区
 * @param {Object|null} community - 小区对象
 */
export const setSelectedCommunity = (community) => {
  selectedCommunity = community

  // 保存到localStorage
  try {
    if (community) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(community))
    } else {
      localStorage.removeItem(STORAGE_KEY)
    }
  } catch (error) {
    console.warn('保存小区数据失败:', error)
  }

  // 通知所有监听器
  changeListeners.forEach(listener => {
    try {
      listener(community)
    } catch (error) {
      console.error('小区变化监听器执行失败:', error)
    }
  })
}

/**
 * 设置小区列表
 * @param {Array} list - 小区列表
 */
export const setCommunityList = (list) => {
  communityList = list || []

  // 保存到localStorage
  try {
    localStorage.setItem(COMMUNITY_LIST_KEY, JSON.stringify(communityList))
  } catch (error) {
    console.warn('保存小区列表失败:', error)
  }

  // 如果有小区数据且没有选中的小区，默认选择第一个
  if (communityList.length > 0 && !selectedCommunity) {
    setSelectedCommunity(communityList[0])
  }
}

/**
 * 获取小区列表
 * @returns {Array} 小区列表
 */
export const getCommunityList = () => {
  return communityList
}

/**
 * 检查是否有选中的小区
 * @returns {boolean} 是否有选中的小区
 */
export const hasSelectedCommunity = () => {
  return !!selectedCommunity
}

/**
 * 添加小区变化监听器
 * @param {Function} listener - 监听器函数
 */
export const addCommunityChangeListener = (listener) => {
  if (typeof listener === 'function') {
    changeListeners.push(listener)
  }
}

/**
 * 移除小区变化监听器
 * @param {Function} listener - 监听器函数
 */
export const removeCommunityChangeListener = (listener) => {
  const index = changeListeners.indexOf(listener)
  if (index > -1) {
    changeListeners.splice(index, 1)
  }
}

/**
 * 清空所有数据
 */
export const clearAllData = () => {
  selectedCommunity = null
  communityList = []
  changeListeners = []

  try {
    localStorage.removeItem(STORAGE_KEY)
    localStorage.removeItem(COMMUNITY_LIST_KEY)
  } catch (error) {
    console.warn('清空存储数据失败:', error)
  }
}

// ==================== 兼容性导出 ====================
export default {
  getSelectedCommunity,
  getSelectedCommunityId,
  setSelectedCommunity,
  setCommunityList,
  getCommunityList,
  hasSelectedCommunity,
  addCommunityChangeListener,
  removeCommunityChangeListener,
  clearAllData
}
